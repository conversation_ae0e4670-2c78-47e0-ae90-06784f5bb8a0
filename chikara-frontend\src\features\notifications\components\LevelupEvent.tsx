import { Sparkles } from "lucide-react";

interface LevelupEventDetails {
    newLevel: number;
}

interface LevelupEventProps {
    details: LevelupEventDetails;
    read: boolean;
}

function LevelupEvent({ details, read }: LevelupEventProps) {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                You leveled up! You reached level {details.newLevel} and gained 50 Health
            </td>
        </>
    );
}

export default LevelupEvent;
