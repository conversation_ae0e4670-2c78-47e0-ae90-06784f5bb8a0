import { DisplayGangIcon } from "@/components/DisplayGangIcon";
import { cn } from "@/lib/utils";
import type { Gang } from "../types/gang";

interface GangBannerProps {
    gang: Gang | null | undefined;
    className?: string;
    onClick?: () => void;
}

export default function GangBanner({ gang, className, onClick }: GangBannerProps) {
    const getMaxMemberCount = (hideoutLevel: number) => {
        // -- TEMP FOR ALPHA --
        if (hideoutLevel === 0) {
            return 5;
        } else {
            return 7;
        }
        // -- TEMP FOR ALPHA --
        // return 5 + hideoutLevel * 3;
    };

    if (!gang) return null;

    return (
        <div
            key={gang.id}
            className={cn(
                "relative flex gap-4 overflow-hidden rounded-lg border-2 border-black bg-linear-to-r from-blue-700 to-blue-900 p-1",
                onClick && "cursor-pointer hover:border-blue-500",
                className
            )}
            onClick={onClick ? onClick : null}
        >
            <div className="relative h-full w-24">
                <DisplayGangIcon src={gang} className="mx-auto h-full w-auto" />
            </div>

            <div className="flex-1">
                <p className="text-left font-body font-semibold text-custom-yellow text-lg text-stroke-sm">
                    {gang.name}
                </p>
                <p className="text-blue-400 text-lg text-stroke-sm">
                    {gang.gang_member ? gang.gang_member.length : gang.memberCount}/
                    {getMaxMemberCount(gang.hideout_level)} <span className="ml-1 text-sm">Members</span>
                </p>
                <div className="flex h-6 items-center gap-0.5">
                    <img
                        className="my-auto mr-1 aspect-square h-6"
                        src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/nf7boKi.png`}
                        alt=""
                    />
                    <p className="mt-0.5 text-sm text-white">Lv {gang.hideout_level}</p>
                </div>
            </div>

            <div className="2 -skew-x-48 absolute bottom-0 left-[76%] size-full overflow-hidden border-black border-l-4 bg-linear-to-r from-indigo-600 to-indigo-900 md:left-[80%]">
                <img
                    className="absolute top-1 left-2 my-auto h-full skew-x-48 p-2 grayscale md:top-0 md:left-4 md:p-1 2xl:p-0"
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/iQwXWBw.png`}
                    alt=""
                />
                {/* <img
          className="h-full ml-1 mt-2 skew-x-48 my-auto p-3"
          src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/GmkUZRW.png`}
          alt=""
        /> */}
            </div>
        </div>
    );
}
