import { DisplayItem } from "@/components/DisplayItem";
import { Modal } from "@/components/Modal/Modal";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { formatCurrency } from "@/utils/currencyHelpers";
import type { InventoryItem } from "@/types/item";

interface SellItemModalProps {
    openModal: boolean;
    setOpenModal: (open: boolean) => void;
    itemToSell: InventoryItem;
}

export default function SellItemModal({ openModal, setOpenModal, itemToSell }: SellItemModalProps) {
    const [quantity, setQuantity] = useState<number | string>(1);

    const queryClient = useQueryClient();

    const item = { ...itemToSell?.item, colour: "" };
    if (item.rarity === "novice") {
        item.colour = "text-stroke-s-sm";
    }
    if (item.rarity === "standard") {
        item.colour = "text-green-600";
    }
    if (item.rarity === "enhanced") {
        item.colour = "text-blue-600";
    }
    if (item.rarity === "specialist") {
        item.colour = "text-indigo-600";
    }
    if (item.rarity === "military") {
        item.colour = "text-red-600";
    }
    if (item.rarity === "legendary") {
        item.colour = "text-yellow-600";
    }

    const sellItem = useMutation(
        api.shop.sellItem.mutationOptions({
            onSuccess: () => {
                setOpenModal(false);
                toast.success(
                    `${Number.parseInt(String(quantity))}x ${item.name} sold for ${formatCurrency((item.cashValue || 0) * Number.parseInt(String(quantity)))}`
                );
                queryClient.invalidateQueries({
                    queryKey: api.user.getInventory.key(),
                });
                queryClient.invalidateQueries({
                    queryKey: api.user.getCurrentUserInfo.key(),
                });
            },
            onError: (error: Error) => {
                setOpenModal(false);
                // Provide user-friendly error messages
                if (error.message.includes("equipped")) {
                    toast.error("Cannot sell equipped items. Please unequip the item first.");
                } else if (error.message.includes("insufficient")) {
                    toast.error("You don't have enough of this item to sell.");
                } else if (error.message.includes("not found")) {
                    toast.error("Item not found in your inventory.");
                } else {
                    toast.error(error.message || "Failed to sell item. Please try again.");
                }
            },
        })
    );

    const handleSell = () => {
        sellItem.mutate({
            userItemId: Number.parseInt(String(itemToSell.id)),
            amount: Number.parseInt(String(quantity)),
        });
        setTimeout(() => setQuantity(1), 500);
    };

    const handleClose = () => {
        setOpenModal(false);
        setTimeout(() => setQuantity(1), 500);
    };

    return (
        <Modal
            showClose
            open={openModal}
            title="SELL ITEM"
            iconBackground="shadow-lg"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/4AAKgyV.png`}
                    alt=""
                    className="mt-0.5 h-11 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="mt-3 text-center">
                <div className="mb-1 flex flex-col">
                    <div className="mb-3 flex flex-col">
                        <DisplayItem item={itemToSell?.item} height="w-1/4 mx-auto" />
                        <p className="text-gray-200">{itemToSell?.item?.name}</p>
                    </div>
                    <label
                        className="my-auto mr-3 block text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300"
                        htmlFor="quantity"
                    >
                        Amount
                    </label>
                    <div className="mx-auto my-2 flex w-2/5 rounded-md shadow-xs md:mt-1">
                        <input
                            type="text"
                            id="quantity"
                            name="quantity"
                            className="block w-full min-w-0 flex-1 rounded-l-md border-gray-300 px-3 py-2 text-center text-lg focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                            placeholder="1"
                            min="1"
                            step="1"
                            max={itemToSell.count}
                            value={quantity}
                            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                                setQuantity(e.target.value);
                            }}
                        />
                        <span className="inline-flex items-center rounded-r-md border border-gray-300 border-l-0 bg-gray-50 px-3 text-center text-gray-500 text-lg sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-950 dark:text-white">
                            / {itemToSell.count}
                        </span>
                    </div>
                </div>
                <p className="mt-2 mb-1 text-base text-gray-500 md:text-lg dark:text-gray-300">
                    Are you sure you want to sell{" "}
                    <span className={item.colour}>
                        {quantity}x {item.name}
                    </span>{" "}
                    for{" "}
                    <span className=" text-green-600">
                        {formatCurrency((item.cashValue || 0) * Number.parseInt(String(quantity)))}
                    </span>
                    ?
                </p>

                <button
                    type="button"
                    className="darkBlueButtonBGSVG mx-auto flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 dark:text-slate-200"
                    onClick={handleSell}
                >
                    Sell
                </button>
            </div>
        </Modal>
    );
}
