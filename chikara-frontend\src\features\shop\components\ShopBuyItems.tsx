import { Fragment, useState } from "react";
import PurchaseItemModal from "./PurchaseItemModal";
import ShopItem from "./ShopItem";
import type { ShopListing, ShopInfo } from "../types/shop";
import type { User } from "@/types/user";

interface ShopBuyItemsProps {
    shopData: ShopInfo;
    currentUser: User;
}

export const ShopBuyItems = ({ shopData, currentUser }: ShopBuyItemsProps) => {
    const [itemToBuy, setItemToBuy] = useState<ShopListing>({
        id: 0,
        cost: 0,
        customCost: 0,
        stock: null,
        item: {
            id: 0,
            name: "",
            image: "",
            itemType: "junk",
            rarity: "novice",
            level: 1,
            upgradeLevel: 0,
            cashValue: 0,
            damage: null,
            armour: null,
            health: null,
            energy: null,
            actionPoints: null,
            baseAmmo: null,
            about: "",
            itemEffects: null,
        },
    });
    const [openModal, setOpenModal] = useState<boolean>(false);

    const sortedListings = shopData?.shop_listing?.sort((a: ShopListing, b: ShopListing) => a.cost - b.cost);

    return (
        <>
            {shopData?.shop_listing?.length === 0 && (
                <div className="flex">
                    <p className="mx-auto mt-12 text-2xl dark:text-gray-200">No items available!</p>
                </div>
            )}
            <div className="mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3">
                {sortedListings?.map((product) => (
                    <Fragment key={product.id}>
                        <ShopItem product={product} setOpenModal={setOpenModal} setItemToBuy={setItemToBuy} />
                    </Fragment>
                ))}
            </div>
            <PurchaseItemModal
                openModal={openModal}
                setOpenModal={setOpenModal}
                itemToBuy={itemToBuy}
                currentUser={currentUser}
            />
        </>
    );
};
