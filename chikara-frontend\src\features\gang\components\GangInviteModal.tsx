import currency2Img from "@/assets/icons/UI/currency2.png";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import LoadingState from "@/components/LoadingState";
import { Modal } from "@/components/Modal/Modal";
import { api } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { Send, X } from "lucide-react";
import toast from "react-hot-toast";
import useInviteToGang from "../api/useInviteToGang";
import type { GangInvite } from "../types/gang";

interface GangInviteModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
}

interface PendingInvitesProps {
    invitedUsers: GangInvite[];
    isLoading: boolean;
}

interface PendingInviteRequestsProps {
    inviteRequests: GangInvite[];
    isLoading: boolean;
    setStudentId: (id: string | undefined) => void;
    handleSubmit: () => void;
}

export default function GangInviteModal({ open, setOpen }: GangInviteModalProps) {
    const { data: pendingInvites, isLoading } = useQuery(api.gang.getInviteList.queryOptions());
    const { studentId, setStudentId, inviteToGang } = useInviteToGang();

    const invitedUsers = pendingInvites?.filter((invite) => invite.inviteType === "invite") || [];
    const inviteRequests = pendingInvites?.filter((invite) => invite.inviteType === "inviteRequest") || [];

    const handleSubmit = () => {
        inviteToGang();
    };

    const handleClose = (change: boolean) => {
        setStudentId(undefined);
        setOpen(change);
    };

    return (
        <Modal
            showClose
            open={open}
            title="Gang Invites"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-3xl!"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Su197a0.png`}
                    alt=""
                    className="mt-0.5 h-10 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="flex flex-col md:mx-12">
                <div>
                    <label
                        className="mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                        htmlFor="inputtitle"
                    >
                        Student ID<span className="text-red-500"> *</span>
                    </label>
                    <div className="mt-1 flex rounded-md shadow-xs">
                        <span className="inline-flex items-center rounded-l-md border border-gray-600 border-r-0 bg-gray-800 px-3 text-gray-300 text-shadow sm:text-sm">
                            #
                        </span>
                        <input
                            type="number"
                            name="studentId"
                            id="studentId"
                            className="block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-600 px-3 py-2 placeholder:text-gray-400 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:bg-gray-900 dark:text-gray-300"
                            value={studentId}
                            onChange={(e) => {
                                setStudentId(e.target.value);
                            }}
                        />
                    </div>
                </div>

                <button
                    type="button"
                    className="darkBlueButtonBGSVG mx-auto mt-4 flex h-14 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-3 dark:text-slate-200"
                    onClick={() => handleSubmit()}
                >
                    Invite
                </button>
                <div className="flex gap-1">
                    <PendingInvites invitedUsers={invitedUsers} isLoading={isLoading} />
                    <PendingInviteRequests
                        inviteRequests={inviteRequests}
                        isLoading={isLoading}
                        setStudentId={setStudentId}
                        handleSubmit={handleSubmit}
                    />
                </div>
            </div>
        </Modal>
    );
}

const PendingInvites = ({ invitedUsers, isLoading }: PendingInvitesProps) => {
    return (
        <div className="mt-4 h-80 w-1/2 overflow-y-auto rounded-lg border border-gray-600 bg-gray-800 p-2 text-gray-200 relative">
            <p className="ml-1 text-sm uppercase leading-none">Pending Invites</p>
            <hr className="mt-1.5 w-full border border-gray-600/75" />
            <div className="mt-1 flex flex-col divide-y divide-gray-600">
                <LoadingState isAbsolute size={12} isLoading={isLoading}>
                    {invitedUsers?.map((invite) => (
                        <div
                            key={invite.id}
                            className="mb-1 flex items-center gap-2 rounded-lg bg-gray-900 p-2 md:py-2.5"
                        >
                            <DisplayAvatar src={invite.recipient} className="rounded-md! my-auto h-6" />
                            <p className="text-xs">
                                {invite.recipient.username}{" "}
                                <span className="text-gray-400 text-xs">#{invite.recipientId}</span>
                            </p>
                            {/* <p className="my-auto text-xs text-gray-400">#{invite.recipientId}</p> */}
                            <div className="flex-1 cursor-pointer" onClick={() => toast.error("Not enabled yet")}>
                                <X className="ml-auto size-4 text-red-500" />
                            </div>
                        </div>
                    ))}
                </LoadingState>
            </div>
        </div>
    );
};

const PendingInviteRequests = ({
    inviteRequests,
    isLoading,
    setStudentId,
    handleSubmit,
}: PendingInviteRequestsProps) => {
    const handleClick = (id: number) => {
        setStudentId(id.toString());
        handleSubmit();
    };
    return (
        <div className="mt-4 h-80 w-1/2 overflow-y-auto rounded-lg border border-gray-600 bg-gray-800 p-2 text-gray-200 relative">
            <p className="ml-1 text-sm uppercase leading-none">Pending Applications</p>
            <hr className="mt-1.5 w-full border border-gray-600/75" />
            <div className="mt-1 flex flex-col divide-y divide-gray-600">
                <LoadingState isAbsolute size={12} isLoading={isLoading}>
                    {inviteRequests?.map((invite) => (
                        <div
                            key={invite.id}
                            className="mb-1 flex items-center gap-2 rounded-lg bg-gray-900 p-2 md:py-2.5"
                        >
                            <DisplayAvatar src={invite.sender} className="rounded-md! my-auto h-6" />
                            <p className="text-xs">
                                {invite.sender.username}{" "}
                                <span className="text-gray-400 text-xs">#{invite.sender.id}</span>
                            </p>
                            <div className="flex-1 cursor-pointer" onClick={() => handleClick(invite.senderId)}>
                                <Send className="ml-auto size-5 text-indigo-300" />
                            </div>
                        </div>
                    ))}
                </LoadingState>
            </div>
        </div>
    );
};
