import useGetUserInfo from "@/hooks/api/useGetUserInfo";
import { cn } from "@/lib/utils";
import { UTCDateMini } from "@date-fns/utc";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import reactStringReplace from "react-string-replace";
import type { User } from "@/types/user";
import type { GangLog } from "../types/gang";

interface GangLogDisplayProps {
    log: GangLog;
    color?: string;
}

type LogAction =
    | "PlayerInvited"
    | "RespectGained"
    | "RespectLost"
    | "GangCreated"
    | "JoinedGang"
    | "LeftGang"
    | "MemberKicked"
    | "HideoutUpgraded"
    | "EssenceGainedRevive";

const formatTime = (date: string) => {
    if (!date) return null;
    const formattedDate = format(new UTCDateMini(date), "HH:mm");
    return formattedDate;
};

const logTexts: Record<LogAction, string> = {
    PlayerInvited: "{gangMember} invited {target} to the gang",
    RespectGained: "{gangMember} defeated {target} {+}{quantity} {Respect}",
    RespectLost: "{gangMember} was defeated by {target} {quantity} {Respect}",
    GangCreated: "Gang Created",
    JoinedGang: "{gangMember} joined the gang",
    LeftGang: "{gangMember} left the gang",
    MemberKicked: "{target} was kicked from the gang by {gangMember}",
    HideoutUpgraded: "Hideout was upgraded to Level {quantity}",
    EssenceGainedRevive: "{gangMember} healed {target} {+}{quantity} {Essence}",
};

const getLogColor = (action: string) => {
    switch (action) {
        case "PlayerInvited":
            return "text-blue-400";
        case "RespectGained":
        case "EssenceGainedRevive":
            return "text-green-400";
        case "RespectLost":
            return "text-red-400";
        case "GangCreated":
            return "text-blue-500";
        case "JoinedGang":
            return "text-blue-500";
        case "LeftGang":
        case "MemberKicked":
            return "text-red-400";
        case "HideoutUpgraded":
            return "text-blue-400";
        default:
            return "text-gray-300";
    }
};

const getLogColorOverride = (action: string) => {
    switch (action) {
        case "GangCreated":
            return "text-blue-500";
        case "JoinedGang":
            return "text-blue-400";
        case "LeftGang":
        case "MemberKicked":
            return "text-red-400";
        case "HideoutUpgraded":
            return "text-blue-400";
        default:
            return "text-gray-200";
    }
};

const getLogText = (log: GangLog, gangMember: User | undefined, targetUser: User | undefined) => {
    let text = logTexts[log.action];
    text = reactStringReplace(text, "{gangMember}", (match, i) => (
        <Link key={`gangMember-${i}`} to={`/profile/${gangMember?.id}`} className="text-custom-yellow">
            {gangMember?.username}
        </Link>
    ));
    text = reactStringReplace(text, "{target}", (match, i) => (
        <Link key={`target-${i}`} to={`/profile/${targetUser?.id}`} className="text-red-600">
            {targetUser?.username}
        </Link>
    ));
    text = reactStringReplace(text, "{quantity}", (match, i) => (
        <span key={`quantity-${i}`} className={getLogColor(log?.action)}>
            {log?.info}
        </span>
    ));
    text = reactStringReplace(text, "{Respect}", (match, i) => (
        <span key={`Respect-${i}`} className={getLogColor(log?.action)}>
            Respect
        </span>
    ));
    text = reactStringReplace(text, "{Essence}", (match, i) => (
        <span key={`Essence-${i}`} className={getLogColor(log?.action)}>
            Essence
        </span>
    ));
    text = reactStringReplace(text, "{+}", (match, i) => (
        <span key={`+-${i}`} className="text-green-400">
            +
        </span>
    ));
    return text;
};

const GangLogDisplay = ({ log, color = "text-gray-300" }: GangLogDisplayProps) => {
    const { data: gangMember } = useGetUserInfo(log.gangMemberId, {
        staleTime: 60000,
        enabled: !!log.gangMemberId,
    });
    const { data: targetUser } = useGetUserInfo(log.secondPartyId, {
        staleTime: 60000,
        enabled: !!log.secondPartyId,
    });
    return (
        <div className={cn("my-auto mb-0.5 flex gap-2 text-xs", color)}>
            <p
                data-tooltip-id="default-tooltip"
                data-tooltip-content={format(new UTCDateMini(log.createdAt), "PP, p")}
                className="w-12 cursor-pointer text-gray-200 text-xs"
            >
                {formatTime(log.createdAt)}
            </p>
            <div>
                <p className={cn(getLogColorOverride(log?.action), "text-wrap text-xs")}>
                    {getLogText(log, gangMember, targetUser)}
                </p>
            </div>
        </div>
    );
};

export default GangLogDisplay;
