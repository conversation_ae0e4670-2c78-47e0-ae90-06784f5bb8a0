import patchNotes from "@/constants/patchNotes.json";
import { Spark<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";

interface PatchNote {
    id: number;
    version: string;
}

interface NewPatchNotesEventDetails {
    patchNoteId: string;
}

interface NewPatchNotesEventProps {
    details: NewPatchNotesEventDetails;
    read: boolean;
}

function NewPatchNotes({ details, read }: NewPatchNotesEventProps) {
    const id = details.patchNoteId;
    const notes = (patchNotes as PatchNote[])?.filter((el) => el.id === Number.parseInt(id))[0] || {};

    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Sparkles className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            {Object.keys(notes)?.length > 0 ? (
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    The game was updated to version:{" "}
                    <span className="mr-2 ml-1 text-indigo-500 text-stroke-sm">{notes.version}</span>
                    <Link
                        className="inline text-sky-500 text-stroke-0 underline"
                        to={{
                            pathname: "/updates",
                            search: `?id=${notes?.id}`,
                        }}
                    >
                        View Patch Notes
                    </Link>
                </td>
            ) : (
                <td
                    className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
                >
                    There is a new game version, please refresh to update.
                </td>
            )}
        </>
    );
}

export default NewPatchNotes;
