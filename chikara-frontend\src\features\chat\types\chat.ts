export interface ParentMessage {
    id: number;
    message: string;
    hidden: boolean;
    userId: number;
    chatRoomId: number;
    parentMessageId: number | null;
    createdAt: string;
    updatedAt: string;
    user: {
        id: number;
        avatar: string | null;
        username: string;
    };
}

export interface ChatMessage {
    id: number;
    message: string;
    hidden: boolean;
    announcementType: string | null;
    createdAt: string;
    updatedAt: string;
    chatRoomId: number;
    userId: number | null;
    parentMessageId: number | null;
    parentMessage: ParentMessage | null;
    user: {
        id: number;
        avatar: string | null;
        username: string;
        userType: string;
        level: number;
    };
}

export interface ChatRoom {
    displayName: string;
    room: {
        id: number;
        name: string;
    };
}
