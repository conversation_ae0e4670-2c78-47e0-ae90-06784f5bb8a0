import { DisplayAvatar } from "@/components/DisplayAvatar";
import CommentDropdownMenu from "@/components/Menu/CommentDropdownMenu";
import { useState } from "react";
import toast from "react-hot-toast";
import { Link } from "react-router-dom";
import { useGetSuggestionComments } from "@/features/suggestions/api/useSuggestions";
import { useSuggestionComment } from "@/features/suggestions/api/useSuggestionMutations";
import type { Suggestion } from "../types/suggestion";
import type { User } from "@/types/user";

export function SuggestionComments({
    suggestion,
    formatTime,
    currentUser,
}: {
    suggestion: Suggestion;
    formatTime: (_date: Date) => string;
    currentUser?: User;
}) {
    const [suggestionComment, setSuggestionComment] = useState("");
    const { data: comments } = useGetSuggestionComments(suggestion.id);
    const addCommentMutation = useSuggestionComment();

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        if (suggestionComment === "") {
            toast.error("Please enter a comment!");
            return;
        }
        if (suggestionComment.length < 5) {
            toast.error("Please enter a minimum of 5 characters!");
            return;
        }

        try {
            await addCommentMutation.mutateAsync({
                id: suggestion.id,
                message: suggestionComment,
            });
            setSuggestionComment("");
        } catch (error) {
            // Error handling is done in the mutation hook
            console.error("Comment error:", error);
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        const inputText = e.target.value;
        const lines = inputText.split("\n");
        let adjustedText = inputText;

        if (lines.length > 10) {
            // Limit the number of lines
            adjustedText = lines.slice(0, 10).join("\n");
        } else if (inputText.length > 800) {
            // Limit the number of characters
            adjustedText = inputText.substring(0, 800);
        }

        setSuggestionComment(adjustedText);
    };

    return (
        <div className="mt-2 border-slate-200 border-t pt-4 dark:border-slate-700">
            <h3 className="mb-2 rounded-lg bg-black/25 py-1 text-center font-medium text-2xl dark:text-slate-100">
                Comments
            </h3>

            <div className="mt-4">
                {comments?.map((comment, index) => (
                    <div
                        key={index}
                        className="flex w-full flex-row border-slate-200 border-t py-4 dark:border-slate-700"
                    >
                        <div className="w-[97%]">
                            <p className="mb-2 w-full whitespace-pre-wrap font-medium text-base text-gray-200">
                                {comment?.message}
                            </p>
                            <div className="flex items-center">
                                <Link
                                    to={`/profile/${comment?.userId}`}
                                    className="mr-2 block font-normal text-blue-400 text-sm"
                                >
                                    <DisplayAvatar
                                        src={comment?.user}
                                        className="mr-1 inline h-5 w-auto rounded-full"
                                    />
                                    {comment?.user?.username}
                                    <small className="ml-0.5 text-slate-400">#{comment?.userId}</small>
                                </Link>
                                <p className="font-medium text-gray-300 text-xs">
                                    Posted {formatTime(comment?.createdAt)} ago
                                </p>
                            </div>
                        </div>
                        {/* <div className="w-[3%] mx-auto text-center relative">
              <CommentDropdownMenu userType={currentUser?.userType} comment={comment} />
            </div> */}
                    </div>
                ))}
            </div>
            <div className="mt-2 min-w-0 flex-1">
                <form onSubmit={handleSubmit}>
                    <div>
                        <textarea
                            id="comment"
                            name="comment"
                            rows={3}
                            className="block w-full rounded-md border border-gray-300 bg-white shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:border-gray-500 dark:bg-gray-700 dark:text-slate-200 dark:text-stroke-sm dark:placeholder:text-gray-300"
                            placeholder="Add a comment.."
                            value={suggestionComment}
                            onChange={handleInputChange}
                        />
                    </div>
                    <div className="flex w-full items-center justify-end">
                        <button className="mt-3 inline-flex rounded-sm bg-linear-to-b from-blue-500 to-indigo-600 px-3 py-2 text-center font-medium text-sm text-stroke-sm text-white transition duration-150 ease-in-out hover:bg-blue-800 focus:outline-hidden focus:ring-4 focus:ring-blue-300 active:outline-hidden active:ring-4 active:ring-blue-300 dark:active:ring-blue-900 dark:focus:ring-blue-900">
                            Leave Comment
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}
