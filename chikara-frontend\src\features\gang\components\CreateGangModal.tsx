import currency2Img from "@/assets/icons/UI/currency2.png";
import { Modal } from "@/components/Modal/Modal";
import { cn } from "@/lib/utils";
import { Bomb, Check, X } from "lucide-react";
import useCreateGang from "../api/useCreateGang";

interface CreateGangModalProps {
    open: boolean;
    setOpen: (open: boolean) => void;
    level: number;
    hasGangItem?: boolean;
}

export default function CreateGangModal({ open, setOpen, level, hasGangItem = false }: CreateGangModalProps) {
    const { gangName, setGangName, gangDescription, setGangDescription, createGang } = useCreateGang(setOpen);

    const handleSubmit = () => {
        createGang();
    };

    const hasRequiredItems = hasGangItem;
    const isLevel20 = level >= 20;

    return (
        <Modal
            showClose
            open={open}
            title="Create a Gang"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-3xl!"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Su197a0.png`}
                    alt=""
                    className="mt-0.5 h-10 w-auto"
                />
            )}
            onOpenChange={setOpen}
        >
            <div className="flex flex-col md:mx-12">
                <div>
                    <label
                        className="mb-2 block text-gray-700 text-sm uppercase tracking-wide dark:text-gray-200"
                        htmlFor="inputtitle"
                    >
                        Gang Name<span className="text-red-500"> *</span>
                    </label>
                    <div className="mt-1 flex rounded-md shadow-xs">
                        <input
                            type="text"
                            name="inputtitle"
                            id="inputtitle"
                            className="block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                            maxLength="40"
                            value={gangName}
                            onChange={(e) => {
                                setGangName(e.target.value);
                            }}
                        />
                    </div>
                </div>

                <div>
                    <label
                        htmlFor="about"
                        className="mt-4 mb-2 block font-bold text-gray-700 text-sm uppercase tracking-wide dark:font-normal dark:text-gray-300"
                    >
                        Gang Description
                    </label>
                    <div className="mt-1 mb-4">
                        <textarea
                            id="about"
                            name="about"
                            rows={2}
                            maxLength="250"
                            className="mt-1 block h-20 w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200"
                            value={gangDescription}
                            onChange={(e) => {
                                setGangDescription(e.target.value);
                            }}
                        />
                    </div>
                </div>
                <div className="mx-4 my-2 flex flex-col rounded-lg border border-gray-600 bg-gray-900 p-2 text-center text-stroke-s-sm text-white">
                    <p className="font-body font-semibold text-white underline underline-offset-2">Requirements:</p>
                    <div className="-mt-1 mx-auto flex gap-2 pr-8 text-base">
                        <img src={currency2Img} alt="" className="mt-0.5 h-10 w-auto" />
                        <div className={cn(hasRequiredItems ? "text-green-500" : "text-red-500", "my-auto")}>
                            1x Gang Sigil{" "}
                            {hasRequiredItems ? (
                                <Check className="mb-0.5 ml-1 inline size-4" />
                            ) : (
                                <X className="mb-0.5 ml-1 inline size-4" />
                            )}
                        </div>
                    </div>
                    <div className="mx-auto flex gap-2 text-base">
                        <div className={cn(isLevel20 ? "text-green-500" : "text-red-500", "my-auto")}>
                            <Bomb className="mr-1.5 mb-0.5 inline size-4 text-custom-yellow" />
                            Level 20{" "}
                            {isLevel20 ? (
                                <Check className="mb-0.5 ml-1 inline size-4" />
                            ) : (
                                <X className="mb-0.5 ml-1 inline size-4" />
                            )}
                        </div>
                    </div>
                </div>
                <button
                    type="button"
                    disabled={!hasRequiredItems || !isLevel20}
                    className="darkBlueButtonBGSVG mx-auto mt-2 flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-3 dark:text-slate-200"
                    onClick={() => handleSubmit()}
                >
                    Create Gang
                </button>
            </div>
        </Modal>
    );
}
