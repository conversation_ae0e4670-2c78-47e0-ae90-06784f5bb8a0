import Button from "@/components/Buttons/Button";
import { Modal } from "@/components/Modal/Modal";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import useUpdateGangInfo from "../api/useUpdateGangInfo";
import type { Gang } from "../types/gang";

interface GangSettingsModalProps {
    gang: Gang | null | undefined;
    open: boolean;
    setOpen: (open: boolean) => void;
}

export default function GangSettingsModal({ gang, open, setOpen }: GangSettingsModalProps) {
    const [gangName, setGangName] = useState(gang?.name || "");
    const [description, setDescription] = useState(gang?.about || "");
    const [gangMOTD, setGangMOTD] = useState(gang?.motd || "");
    const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);
    const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

    const { updateGangInfo, isLoading } = useUpdateGangInfo();

    const handleClose = (change: boolean) => {
        setGangName(gang?.name || "");
        setDescription(gang?.about || "");
        setGangMOTD(gang?.motd || "");
        setOpen(change);
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        if (description && description.length < 5) {
            toast.error("Description is too short!");
            return;
        }
        if (description && description.length > 500) {
            toast.error("Description is too long!");
            return;
        }
        if (gangMOTD && gangMOTD.length > 500) {
            toast.error("MOTD is too long!");
            return;
        }

        updateGangInfo({
            about: description,
            motd: gangMOTD,
            gang_avatar: selectedAvatar,
        });
    };

    useEffect(() => {
        if (!selectedAvatar) {
            if (gang?.avatar) {
                setAvatarPreview(`/${gang?.avatar}`);
            } else {
                setAvatarPreview(null);
            }

            return;
        }
        const objectUrl = URL.createObjectURL(selectedAvatar);
        setAvatarPreview(objectUrl);

        // free memory when ever this component is unmounted
        return () => URL.revokeObjectURL(objectUrl);
    }, [selectedAvatar]);

    return (
        <Modal
            showClose
            open={open}
            title="Gang Settings"
            iconBackground="shadow-lg"
            modalMaxWidth="max-w-3xl!"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Su197a0.png`}
                    alt=""
                    className="mt-0.5 h-10 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="flex flex-col md:mx-8">
                <div className="mt-2 flex flex-col rounded-lg border border-gray-600 bg-gray-800 px-3 py-4 lg:flex-row">
                    <div className="grow space-y-6">
                        <div>
                            <label
                                htmlFor="username"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Gang Name
                            </label>
                            <div className="mt-1 flex rounded-md shadow-xs">
                                <input
                                    disabled
                                    //   onChange={(e) => {
                                    //     setGangName(e.target.value);
                                    //   }}
                                    value={gangName}
                                    type="text"
                                    name="username"
                                    id="username"
                                    autoComplete="username"
                                    className="block w-full min-w-0 grow rounded-md border-gray-300 focus:border-light-blue-500 focus:ring-light-blue-500 disabled:bg-gray-600 sm:text-sm dark:border-gray-600 dark:text-gray-200"
                                />
                            </div>
                        </div>

                        <div>
                            <label
                                htmlFor="about"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Gang Description
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="about"
                                    name="about"
                                    rows={4}
                                    maxLength={500}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200"
                                    value={description}
                                    onChange={(e) => {
                                        setDescription(e.target.value);
                                    }}
                                />
                            </div>
                        </div>
                        <div>
                            <label
                                htmlFor="motd"
                                className="mb-2 block font-bold text-gray-700 text-xs uppercase tracking-wide dark:font-normal dark:text-gray-300"
                            >
                                Gang Message of the Day{" "}
                                <span className="text-orange-700">
                                    {gang?.hideout_level < 2 ? "(Requires Hideout Level 2)" : ""}
                                </span>
                            </label>
                            <div className="mt-1">
                                <textarea
                                    id="motd"
                                    name="motd"
                                    rows={3}
                                    disabled={gang?.hideout_level < 2}
                                    maxLength={500}
                                    className="mt-1 block w-full rounded-md border-gray-300 shadow-xs focus:border-light-blue-500 focus:ring-light-blue-500 sm:text-sm dark:border-gray-600 dark:bg-slate-900 dark:text-gray-200 dark:disabled:bg-gray-700"
                                    value={gangMOTD}
                                    onChange={(e) => {
                                        setGangMOTD(e.target.value);
                                    }}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="mt-6 grow lg:mt-0 lg:ml-6 lg:shrink-0 lg:grow-0">
                        <p
                            className="mb-3 block font-bold text-gray-700 text-xs uppercase tracking-wide md:text-center dark:font-normal dark:text-gray-300"
                            aria-hidden="true"
                        >
                            Icon
                        </p>
                        <div className="mt-1 lg:hidden">
                            <div className="flex items-center">
                                <div
                                    className="inline-block size-12 shrink-0 overflow-hidden rounded-full"
                                    aria-hidden="true"
                                >
                                    <img className="size-full rounded-full" src={avatarPreview} alt="" />
                                </div>
                                <div className="ml-5 rounded-md shadow-xs">
                                    <div className="group relative flex items-center justify-center rounded-md border border-gray-300 px-3 py-2 focus-within:ring-2 focus-within:ring-light-blue-500 focus-within:ring-offset-2 hover:bg-gray-50 dark:border-blue-600 dark:bg-blue-700 dark:text-stroke-sm">
                                        <label
                                            htmlFor="user-photo-mobile"
                                            className="pointer-events-none relative font-medium text-gray-700 text-sm leading-4 dark:font-normal dark:text-white"
                                        >
                                            {/* MOBILE */}
                                            <span>Change Icon</span>
                                            <span className="sr-only"> user photo</span>
                                        </label>
                                        <input
                                            id="user-photo-mobile"
                                            name="user-photo-mobile"
                                            type="file"
                                            accept="image/*"
                                            className="absolute size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                            onChange={(e) => setSelectedAvatar(e.target.files?.[0] || null)}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="relative hidden overflow-hidden rounded-full lg:block">
                            <img className="relative size-40 rounded-full" src={avatarPreview} alt="" />
                            <label
                                htmlFor="user-photo"
                                className="absolute inset-0 flex size-full items-center justify-center bg-black/75 font-medium text-sm text-white opacity-0 focus-within:opacity-100 hover:opacity-100"
                            >
                                {/* DESKTOP */}
                                <span>Change</span>
                                <span className="sr-only"> user photo</span>
                                <input
                                    type="file"
                                    id="user-photo"
                                    name="user-photo"
                                    accept="image/*"
                                    className="absolute inset-0 size-full cursor-pointer rounded-md border-gray-300 opacity-0"
                                    onChange={(e) => setSelectedAvatar(e.target.files?.[0] || null)}
                                />
                            </label>
                        </div>
                    </div>
                </div>

                <button
                    type="submit"
                    disabled={isLoading}
                    className="darkBlueButtonBGSVG mx-auto mt-4 flex h-14 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs disabled:opacity-75 disabled:grayscale md:mt-6 dark:text-slate-200"
                    onClick={(e) => handleSubmit(e)}
                >
                    {isLoading ? "Saving..." : "Save"}
                </button>
                <hr className="my-3 w-full border border-gray-600/50" />
                <div className="mt-1 flex w-full">
                    <Button disabled className="w-32! ml-auto!" variant="destructive">
                        Disband Gang
                    </Button>
                </div>
            </div>
        </Modal>
    );
}
