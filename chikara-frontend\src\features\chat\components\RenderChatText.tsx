import { cn } from "@/lib/utils";
import { Fragment } from "react";
import { Link } from "react-router-dom";
import emotes from "../data/chatEmotes.json";
import { getEmoteSrc } from "../helpers/chatHelpers";
import { ChatMessage, ParentMessage } from "../types/chat";

interface RenderChatTextProps {
    msg: ChatMessage | ParentMessage | null;
    className?: string;
    imgClassName?: string | null;
}

export default function RenderChatText({ msg, className, imgClassName = null }: RenderChatTextProps) {
    if (!msg || !msg?.message) return null;

    const replaceText = (text: string) => {
        const textSegments = text.split(/(:[a-zA-Z0-9_]+|@[a-zA-Z0-9_]+)/g);
        return textSegments.map((segment, index) => {
            // Handle emotes
            const filteredEmote = emotes[segment.replace(":", "") as keyof typeof emotes];
            if (filteredEmote) {
                return (
                    <img
                        key={index}
                        src={getEmoteSrc(filteredEmote)}
                        alt={segment}
                        // Makes emote bigger if it's the only thing in the message
                        className={cn(segment === msg?.message ? "h-20 w-auto" : "inline h-10 w-auto", imgClassName)}
                    />
                );
            }

            // Handle mentions
            if (segment.startsWith("@")) {
                const username = segment.slice(1);
                return (
                    <span
                        key={index}
                        className="text-gray-900! text-xs! rounded-md bg-custom-yellow px-1 py-[0.05rem] font-bold text-stroke-0"
                    >
                        @{username}
                    </span>
                );
            }

            return segment;
        });
    };

    if (msg?.user?.userType === "admin" && msg?.message?.startsWith("/link")) {
        const messageContents = msg?.message?.slice(6);
        const [href, linkText] = messageContents.split("|");
        return (
            <Link
                className="font-body text-blue-500 text-sm text-stroke-0 underline md:text-[0.8rem] md:leading-[1.2rem]"
                to={href}
            >
                {linkText ? linkText : href}
            </Link>
        );
    }

    if (msg?.message?.startsWith("/me")) {
        const messageContents = msg?.message?.slice(4);
        return (
            <p className="my-auto w-[90%] break-words font-body text-gray-700 text-sm italic md:text-[0.8rem] md:leading-[1.2rem] dark:text-slate-300">
                {msg?.user?.username} {messageContents}
            </p>
        );
    }

    if (msg?.user?.userType === "admin" && msg?.message?.startsWith("/externallink")) {
        const messageContents = msg?.message?.slice(14);
        const [href, linkText] = messageContents.split("|");
        return (
            <a
                className="font-body text-blue-500 text-sm text-stroke-0 underline md:text-[0.8rem] md:leading-[1.2rem]"
                href={href}
                target="_blank"
                rel="noreferrer"
            >
                {linkText ? linkText : href}
            </a>
        );
    }

    return (
        <div className={className}>
            {replaceText(msg?.message).map((element, index) => (
                <Fragment key={index}>{element}</Fragment>
            ))}
        </div>
    );
}

// const filteredMention =
// msg?.userId !== currentUser.id && segment.includes(`@${currentUser?.username}`);
// if (filteredMention) {
//   return (
//     <span
//       key={index}
//       className="bg-custom-yellow text-gray-900! text-stroke-0 rounded-md px-1 py-[0.05rem] font-bold text-xs! "
//     >
//       @{currentUser?.username}
//     </span>
//   );
// }
