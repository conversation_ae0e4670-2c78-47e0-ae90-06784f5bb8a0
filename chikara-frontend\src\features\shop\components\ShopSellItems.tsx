import { useState } from "react";
import SellItemModal from "./SellItemModal";
import ShopItemSell from "./ShopItemSell";
import useGetInventory from "@/hooks/api/useGetInventory";
import type { InventoryItem } from "@/types/item";
import type { User } from "@/types/user";

interface ShopSellItemsProps {
    currentUser: User;
}

// const isEquipped = (itemId: number, user: User) => {
//   return [
//     user.equippedWeaponId,
//     user.equippedRangedWeaponId,
//     user.equippedHeadId,
//     user.equippedChestId,
//     user.equippedHandsId,
//     user.equippedLegsId,
//     user.equippedFeetId,
//     user.equippedFingerId,
//     user.equippedOffhandId,
//   ].includes(itemId);
// };

export const ShopSellItems = ({ currentUser }: ShopSellItemsProps) => {
    const [itemToSell, setItemToSell] = useState<InventoryItem>({
        id: 0,
        count: 0,
        item: {
            id: 0,
            name: "",
            image: "",
            itemType: "junk",
            rarity: "novice",
            level: 1,
            upgradeLevel: 0,
            cashValue: 0,
            damage: null,
            armour: null,
            health: null,
            energy: null,
            actionPoints: null,
            baseAmmo: null,
            about: "",
            itemEffects: null,
        },
    });
    const [openModal, setOpenModal] = useState<boolean>(false);

    const { data, isLoading } = useGetInventory();

    const sortedInventory = data?.sort(
        (a: InventoryItem, b: InventoryItem) => (a?.item?.cashValue || 0) - (b?.item?.cashValue || 0)
    );
    const filteredInventory = sortedInventory?.filter((item: InventoryItem) => item?.item?.itemType !== "quest");

    if (isLoading) return "Loading..";

    return (
        <>
            {data?.length === 0 && (
                <div className="flex">
                    <p className="mx-auto mt-12 text-2xl dark:text-gray-200">You have no items to sell!</p>
                </div>
            )}
            <div className="mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3">
                {filteredInventory?.map((item: InventoryItem) => (
                    <ShopItemSell key={item.id} item={item} setOpenModal={setOpenModal} setItemToSell={setItemToSell} />
                ))}
            </div>
            <SellItemModal openModal={openModal} setOpenModal={setOpenModal} itemToSell={itemToSell} />
        </>
    );
};
