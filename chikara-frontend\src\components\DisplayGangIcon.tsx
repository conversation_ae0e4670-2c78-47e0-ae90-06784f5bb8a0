import { cn } from "@/lib/utils";
import type { Gang } from "@/features/gang/types/gang";
import defaultGangIcon from "@/assets/icons/navitems/gang.png";

const CDN_URL = import.meta.env.VITE_IMAGE_CDN_URL || import.meta.env.VITE_API_BASE_URL;
const DEFAULT_GANG_ICON = defaultGangIcon || `https://cloudflare-image.jamessut.workers.dev/ui-images/OMun8OF.png`;

const gangURL = (gang: Gang | null | undefined): string => {
    if (gang?.avatar) {
        if (gang?.avatar.startsWith("http")) {
            return gang.avatar;
        }
        return `${CDN_URL}/${gang.avatar}`;
    } else {
        return DEFAULT_GANG_ICON;
    }
};

interface DisplayGangIconProps {
    src?: Gang | null;
    className?: string;
    onAnimationEnd?: () => void;
    loading?: "eager" | "lazy";
}

export const DisplayGangIcon = ({ src, className, onAnimationEnd, loading }: DisplayGangIconProps) => {
    return (
        <img
            src={gangURL(src)}
            className={cn(className ? className : "size-full rounded-md object-cover")}
            alt={`Gang Avatar`}
            loading={loading}
            onAnimationEnd={() => onAnimationEnd?.()}
            onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.onerror = null;
                target.src = DEFAULT_GANG_ICON;
            }}
        />
    );
};
