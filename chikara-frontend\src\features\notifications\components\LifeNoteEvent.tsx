import { Flame } from "lucide-react";

interface LifeNoteEventProps {
    details: any; // This component doesn't seem to use details
    read: boolean;
}

function LifeNoteEvent({ details, read }: LifeNoteEventProps) {
    return (
        <>
            <td
                className={`py-4 pr-3 pl-4 font-medium text-gray-900 text-xs sm:pl-6 md:text-sm ${
                    !read && "bg-blue-50 dark:bg-blue-900"
                }`}
            >
                <Flame className="size-6 shrink-0 text-gray-800 dark:text-gray-50" aria-hidden="true" />
            </td>
            <td
                className={`px-3 py-4 text-gray-500 text-xs md:text-sm dark:text-gray-50 ${!read && "bg-blue-50 dark:bg-blue-900"}`}
            >
                A kind stranger decided to heal you! You were fully cured of your injuries.
            </td>
        </>
    );
}

export default LifeNoteEvent;
