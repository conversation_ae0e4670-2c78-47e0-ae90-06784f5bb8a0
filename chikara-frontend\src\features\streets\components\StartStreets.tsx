import { useBeginRun } from "../api/useRoguelikeMutations";

export default function StartStreets() {
    const beginRunMutation = useBeginRun();

    const startAdventure = () => {
        beginRunMutation.mutate({
            location: "school",
            level: 1,
        });
    };

    return (
        <div className="relative my-auto h-full overflow-hidden shadow-sm md:h-full md:rounded-lg">
            <img
                className="h-full object-cover md:h-full md:scale-105 md:rounded-lg"
                src="https://d13cmcqz8qkryo.cloudfront.net/static/backgrounds/exterior/city/street1Day.webp"
                alt=""
            />
            <div className="absolute bottom-0 size-full bg-black opacity-40 md:rounded-t-lg"></div>

            <div className="-translate-x-1/2 absolute bottom-1/2 left-1/2 flex w-[88%] translate-y-1/2 flex-col rounded-lg border border-slate-200 bg-slate-800/90 p-4 text-sm text-white md:w-1/2 md:p-5 md:text-base">
                <p className="mb-2 text-center text-2xl uppercase">Streets</p>
                <p>
                    Streets is an endless roguelite gamemode that takes you on a journey through the perilous streets of
                    Tokyo.
                </p>
                <p className="mt-6">
                    As you navigate through the streets, you&apos;ll face off against hostile enemies, interact with
                    intriguing characters who may offer you valuable rewards(or attempt to rob you), and collect
                    temporary buffs to enhance your performance during the run.
                </p>
                <p className="mt-6">Defeat will reset you to the start of the zone.</p>
                <div className="mx-auto mt-6 md:mt-6">
                    <button
                        className="mx-2 w-32 rounded-md border border-transparent bg-indigo-600 px-3 py-2 font-medium text-md text-white shadow-xs hover:bg-indigo-700 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:px-4"
                        onClick={() => startAdventure()}
                    >
                        Begin
                    </button>
                </div>
            </div>
        </div>
    );
}
