import { describe, expect, it } from "vitest";
import { getObjectiveText } from "../useGetQuestObjectiveText";
import { QuestObjectiveTypes } from "@/types/quest";

describe("getObjectiveText for GATHER_RESOURCES", () => {
    it("should handle specific item + specific activity", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 5,
            target: 200,
            targetAction: "mining",
            item: { id: 200, name: "Copper Ore" },
            itemId: 200,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 5 Copper Ore through Mining");
    });

    it("should handle any item + specific activity", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 3,
            target: null,
            targetAction: "scavenging",
            item: null,
            itemId: null,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 3 items through Scavenging");
    });

    it("should handle specific item + any activity", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 2,
            target: 202,
            targetAction: null,
            item: { id: 202, name: "Silver Ore" },
            itemId: 202,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 2 Silver Ore through any activity");
    });

    it("should handle foraging with plural plants", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 4,
            target: null,
            targetAction: "foraging",
            item: null,
            itemId: null,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 4 plants through Foraging");
    });

    it("should handle mining with singular ore", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 1,
            target: null,
            targetAction: "mining",
            item: null,
            itemId: null,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 1 ore through Mining");
    });

    it("should handle unknown activity type", () => {
        const quest = {
            objectiveType: QuestObjectiveTypes.GATHER_RESOURCES,
            quantity: 2,
            target: null,
            targetAction: "unknown",
            item: null,
            itemId: null,
        } as any;

        const result = getObjectiveText(quest);
        expect(result).toBe("Gather 2 resources through Unknown");
    });
});
