import { motion } from "framer-motion";
import emotes from "../data/chatEmotes.json";
import { getEmoteSrc } from "../helpers/chatHelpers";
import { User } from "@/types/user";

interface EmotePickerProps {
    userMessage: string;
    setUserMessage: (message: string) => void;
    innerRef: React.RefObject<HTMLDivElement>;
    currentUser: User;
}

export default function EmotePicker({ userMessage, setUserMessage, innerRef, currentUser }: EmotePickerProps) {
    const handleClick = (emojiKey: string) => {
        if (userMessage.length > 0) {
            setUserMessage(userMessage + " :" + emojiKey);
        } else {
            setUserMessage(userMessage + ":" + emojiKey);
        }
    };

    const lockedEmotes: string[] = [];
    if (currentUser?.userType !== "admin") {
        lockedEmotes.push("dies");
    }

    return (
        <motion.div
            ref={innerRef}
            initial={{ y: -50, opacity: 0, transition: { type: "easeIn", duration: 0.1 } }}
            animate={{ y: 0, opacity: 1, transition: { duration: 0.1 } }}
            exit={{ y: -50, opacity: 0, transition: { type: "easeOut", duration: 0.1 } }}
            className="scrollbar absolute right-0 bottom-22 z-50 mx-2 h-72 w-1/2 min-w-fit overflow-y-scroll rounded-lg border border-gray-700 bg-gray-900"
        >
            <div className="grid grid-cols-5 gap-3 p-2 md:grid-cols-4 2xl:grid-cols-5">
                {Object.keys(emotes)
                    .sort()
                    .filter((emote) => !lockedEmotes.includes(emote))
                    .map((emojiKey, i) => (
                        <div
                            key={i}
                            className="mx-auto flex size-16 cursor-pointer flex-col rounded-lg p-2 hover:bg-gray-800"
                            onClick={() => handleClick(emojiKey)}
                        >
                            <img
                                className="mx-auto size-12 select-none shadow-xl"
                                src={getEmoteSrc(emotes[emojiKey as keyof typeof emotes])}
                                alt=""
                            />
                            <p className="mx-auto text-gray-200 text-sm text-stroke-s-sm">:{emojiKey}</p>
                        </div>
                    ))}
            </div>
        </motion.div>
    );
}
