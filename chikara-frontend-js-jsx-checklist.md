# Chikara Frontend JS/JSX Files Checklist

This document contains a checklist of all remaining `.js` and `.jsx` files in the `chikara-frontend/src` directory that may need to be migrated to TypeScript.

## Components

## Features

### Casino

- [ ] `features/casino/components/WheelSpin.jsx`

### Classroom

- [ ] `features/classroom/components/ClassChatroom.jsx`
- [ ] `features/classroom/components/ClassShop.jsx`
- [ ] `features/classroom/components/ClassTabs.jsx`
- [ ] `features/classroom/components/Exam.jsx`
- [ ] `features/classroom/components/ExamRanking.jsx`
- [ ] `features/classroom/components/ExamsTab.jsx`

### Daily Task

- [ ] `features/dailytask/components/DailyRewardDisplay.jsx`

### Faculty List

- [ ] `features/facultylist/ClassFilter.jsx`
- [ ] `features/facultylist/RoleFilter.jsx`

### Home

- [ ] `features/home/<USER>/HomeCalendar.jsx`
- [ ] `features/home/<USER>/HomeNavButton.jsx`

### Hospital

- [ ] `features/hospital/components/HospitalInjuryPanel.jsx`
- [ ] `features/hospital/components/HospitalisationReason.jsx`

### News

- [ ] `features/news/components/ChangelogCard.jsx`
- [ ] `features/news/components/PostContent.jsx`
- [ ] `features/news/components/PublicNewsWrapper.jsx`

## Pages

- [ ] `pages/AdventurePage.jsx`
- [ ] `pages/Calendar.jsx`
- [ ] `pages/Casino.jsx`
- [ ] `pages/Classroom.jsx`
- [ ] `pages/CombinedExplore.jsx`
- [ ] `pages/ConstructionPage.jsx`
- [ ] `pages/Courses.jsx`
- [ ] `pages/DailyTaskPage.jsx`
- [ ] `pages/Events.jsx`
- [ ] `pages/Explore.jsx`
- [ ] `pages/FullChat.jsx`
- [ ] `pages/GameStats.jsx`

## Migration Notes

Convert the following javascript (.jsx) files to Typescript.

- Remember that api responses are already fully typed.
- Do not use return types.

When migrating these files to TypeScript:

1. Rename file extensions from `.jsx` to `.tsx`
2. Add proper TypeScript types for props, state, and function parameters
3. Update any import statements that may need type adjustments
