import gangCredImg from "@/assets/icons/UI/currency2.png";
import levelBG from "@/assets/icons/UI/expBG.png";
import { ItemComparisonArrow } from "@/components/Items/ItemComparisonArrow";
import { Modal } from "@/components/Modal/Modal";
import { api } from "@/helpers/api";
import { displayMissingIcon } from "@/helpers/displayMissingIcon";
import { getItemType, getItemTypeIcon } from "@/helpers/itemHelpers";
import { itemTreatmentDisplay } from "@/helpers/itemTreatmentDisplay";
import { cn } from "@/lib/utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Heart } from "lucide-react";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { itemStatDisplay } from "../../../components/Inventory/itemStatDisplay";
import { getCurrencySymbol, formatCurrency } from "@/utils/currencyHelpers";
import type { ItemRarity } from "@/types/item";
import type { User } from "@/types/user";
import type { ShopListing, AuctionItem, CurrencyType } from "../types/shop";

interface PurchaseItemModalProps {
    openModal: boolean;
    setOpenModal: (open: boolean) => void;
    itemToBuy: ShopListing | AuctionItem;
    currencyType?: CurrencyType;
    currentUser: User;
}

export default function PurchaseItemModal({
    openModal,
    setOpenModal,
    itemToBuy,
    currencyType = "yen",
    currentUser,
}: PurchaseItemModalProps) {
    const queryClient = useQueryClient();
    const [quantity, setQuantity] = useState<Record<string, string>>({});
    const itemCost =
        (itemToBuy as ShopListing)?.customCost > 0
            ? (itemToBuy as ShopListing)?.customCost
            : (itemToBuy as ShopListing)?.cost;
    const isMarketItem = !!(itemToBuy as AuctionItem)?.status;

    const { data: equippedItems } = useQuery(api.user.getEquippedItems.queryOptions());

    const buyItem = useMutation({
        mutationFn: async () => {
            const selectedQuantity = Number.parseInt(quantity["quantity" + itemToBuy.id]) || 1;

            if (isMarketItem) {
                // Call ORPC auction function directly
                await api.auctions.buyoutListing.call({
                    auctionItemId: itemToBuy.id,
                    quantity: selectedQuantity,
                });
            } else {
                // Call ORPC shop function directly
                await api.shop.purchaseItem.call({
                    id: itemToBuy.id,
                    amount: selectedQuantity,
                });
            }

            return selectedQuantity; // Return the quantity for use in onSuccess
        },
        onSuccess: (selectedQuantity: number) => {
            const totalCost = selectedQuantity * (isMarketItem ? (itemToBuy as AuctionItem)?.buyoutPrice : itemCost);

            if (isMarketItem) {
                queryClient.invalidateQueries({
                    queryKey: api.auctions.getList.key(),
                });
            } else if ((itemToBuy as ShopListing).stock !== null) {
                queryClient.invalidateQueries({
                    queryKey: api.shop.shopInfo.key(),
                });
            }
            queryClient.invalidateQueries({
                queryKey: api.user.getCurrentUserInfo.key(),
            });
            queryClient.invalidateQueries({
                queryKey: api.user.getInventory.key(),
            });

            toast.success(
                `You purchased ${selectedQuantity}x ${itemToBuy.item.name} for ${currencyType === "yen" ? formatCurrency(totalCost) : totalCost}`
            );
        },
        onError: (e: Error) => {
            if (isMarketItem) {
                console.log(e);
            } else {
                toast.error(e.message);
            }
        },
    });

    const handleBuy = () => {
        const shopItem = itemToBuy as ShopListing;
        if (shopItem?.stock !== null) {
            if (shopItem?.stock === 0) {
                toast.error("Item is out of stock!");
                return;
            }
            if (shopItem?.stock < Number.parseInt(quantity[`quantity${itemToBuy.id}`] || "1")) {
                toast.error("Not enough stock!");
                return;
            }
        }
        buyItem.mutate();
        setOpenModal(false);
        setTimeout(() => setQuantity({}), 500);
    };

    const itemBackground = (rarity: ItemRarity | undefined) => {
        if (rarity === "novice") {
            return "from-gray-700/85 to-gray-800 text-gray-100 text-stroke-sm";
        }
        if (rarity === "standard") {
            return "from-green-800 via-emerald-700 to-green-800 text-gray-100 text-stroke-sm";
        }
        if (rarity === "enhanced") {
            return "from-blue-700 via-blue-800 to-blue-800 text-white text-stroke-sm";
        }
        if (rarity === "specialist") {
            return "from-violet-500 via-purple-600 to-purple-700 text-white text-stroke-sm";
        }
        if (rarity === "military") {
            return "from-red-500 via-crimson-500 to-red-700 text-white text-stroke-sm";
        }
        if (rarity === "legendary") {
            return "from-amber-300 via-amber-500 to-amber-600 text-white text-stroke-s-sm";
        }
        return "";
    };

    const handleQuantityChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
        setQuantity({ ...quantity, [e.target.name]: e.target.value });
    };

    const handleClose = () => {
        setOpenModal(false);
        setTimeout(() => setQuantity({}), 500);
    };

    const displayCurrency = () => {
        if (currencyType === "yen") {
            return getCurrencySymbol("yen");
        }
        if (currencyType === "gangCreds") {
            return <img src={gangCredImg} alt="" className="mx-0.5 mb-0.5 inline h-8 w-auto" />;
        }
        if (currencyType === "classPoints") {
            return (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/Ou05yxV.png`}
                    alt=""
                    className="mr-1 mb-0.5 ml-0.5 inline h-8 w-auto"
                />
            );
        }

        return getCurrencySymbol("yen");
    };

    const getQuantityOptions = (isMarketItemParam: boolean) => {
        const optionLength = isMarketItemParam ? (itemToBuy as AuctionItem)?.quantity : 10;
        return (
            <>
                {[...new Array(optionLength)].map((_, i) => (
                    <option key={i} value={i + 1}>
                        {i + 1}
                    </option>
                ))}
            </>
        );
    };

    const getStatType = (statString: string) => {
        if (!statString) return null;

        if (statString.includes("ARMOR")) return "armour";
        if (statString.includes("DMG")) return "damage";
        if (statString.includes("STR")) return "modifiers";
        if (statString.includes("DEF")) return "modifiers";
        if (statString.includes("DEX")) return "modifiers";
        if (statString.includes("STA")) return "modifiers";
        if (statString.includes("LIFE STEAL")) return "modifiers";
        return null;
    };

    const levelBGStyle = {
        backgroundImage: `url(${levelBG})`,
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
    };
    const treatmentsArray = itemTreatmentDisplay({ item: itemToBuy?.item });

    const ItemLevelReq = () => {
        const invalidTypes = ["special", "quest", "junk", "crafting", "consumable", "upgrade", "pet", "pet_food"];
        if (invalidTypes.includes(itemToBuy?.item?.itemType)) {
            return null;
        }

        const itemLevel = itemToBuy?.item?.level;

        return (
            <div className="m-auto flex gap-1.5">
                <p className="my-auto text-sm">LEVEL</p>
                <div style={levelBGStyle} className="relative z-15 my-auto h-[1.8rem] w-6">
                    <p
                        className={cn(
                            itemLevel === 1 && "pl-px",
                            itemLevel > 9 ? "text-sm" : "text-base",
                            itemLevel > currentUser?.level && "text-stroke-0! font-medium text-red-500",
                            "-translate-x-1/2 -translate-y-1/2 absolute top-[47%] left-1/2 z-20 w-auto object-contain object-center text-stroke-s-sm"
                        )}
                    >
                        {itemLevel}
                    </p>
                </div>
            </div>
        );
    };

    return (
        <Modal
            showClose
            open={openModal}
            title="PURCHASE ITEM"
            iconBackground="shadow-lg"
            Icon={() => (
                <img
                    src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/JXozQjh.png`}
                    alt=""
                    className="mt-0.5 h-11 w-auto"
                />
            )}
            onOpenChange={handleClose}
        >
            <div className="flex flex-col">
                <div
                    className={`relative mt-2 flex w-full rounded-lg border-2 bg-linear-to-b p-2 pr-1 dark:border-black ${itemBackground(
                        itemToBuy?.item?.rarity
                    )}`}
                >
                    <div className="relative flex w-28 flex-col gap-2">
                        <img className="z-100" src={displayMissingIcon(itemToBuy?.item.image)} alt="" />
                        <ItemLevelReq />
                        {itemToBuy?.item?.itemType === "recipe" && (
                            <img
                                src={`${import.meta.env.VITE_IMAGE_CDN_URL}/ui-images/vZhb8GS.png`}
                                className="-translate-x-1/2 -translate-y-1/2 absolute top-[33%] left-1/2 h-4/5 scale-110 opacity-95 brightness-[0.8]"
                                alt=""
                            />
                        )}
                    </div>
                    {/* <div className="h-20 w-28 relative">
            <div
              style={levelBGStyle}
              className="h-7 w-6 z-15 absolute -bottom-1 -right-1 my-auto"
            >
              <p
                className={cn(
                  itemToBuy?.item?.level === 1 && "pl-px",
                  itemToBuy?.item?.level > 9 ? "text-xs" : "text-sm",
                  "w-auto absolute object-contain object-center top-[47%] left-1/2 -translate-x-1/2 -translate-y-1/2 z-20",
                )}
              >
                {itemToBuy?.item?.level}
              </p>
            </div>
            <img className="" src={displayMissingIcon(itemToBuy?.item.image)} alt="" />
          </div> */}
                    {/* <img className="h-20 w-20" src={displayMissingIcon(itemToBuy?.item.image)} alt="" /> */}

                    <div className="mx-4 flex w-full flex-col">
                        <div className="flex justify-between">
                            <div className="flex flex-row gap-1.5 pr-16 text-lg text-stroke-s-sm text-white">
                                {itemToBuy?.item.name}{" "}
                            </div>
                            <div className="-mr-2 absolute top-1.5 right-3 my-auto flex h-fit flex-row gap-1 rounded-lg px-2 text-gray-100 text-md text-stroke-sm">
                                <img src={getItemTypeIcon(itemToBuy?.item)} alt="" className="mt-0.5 h-5 w-auto" />
                                <div className="my-auto">{getItemType(itemToBuy?.item.itemType)}</div>
                            </div>
                        </div>
                        <p className="text-sm">{itemToBuy?.item.about}</p>
                        {itemToBuy?.item?.baseAmmo > 0 && (
                            <div className="mt-1 flex justify-between">
                                <p
                                    className={`my-auto flex flex-col rounded-lg border border-black/50 bg-black/35 px-2 text-base text-sky-400 text-stroke-sm md:flex-row md:gap-1 md:text-sm`}
                                >
                                    2 Base Ammo
                                </p>
                            </div>
                        )}

                        <div className="mt-1 flex justify-between">
                            {itemToBuy?.item.itemType !== "crafting" &&
                                itemToBuy?.item.itemType !== "upgrade" &&
                                itemToBuy?.item.itemType !== "junk" && (
                                    <p
                                        className={`my-auto flex flex-col rounded-lg border border-black/50 bg-black/35 px-2 py-0.5 text-base text-blue-500 text-stroke-sm md:flex-row md:gap-1 md:text-sm ${
                                            itemToBuy?.item.rarity === "legendary"
                                                ? "text-sky-400"
                                                : "text-custom-yellow"
                                        }`}
                                    >
                                        {itemStatDisplay(itemToBuy?.item).map((stat) => (
                                            <div key={stat} className="flex">
                                                <p>{stat}</p>

                                                <ItemComparisonArrow
                                                    item={itemToBuy?.item}
                                                    equippedItems={equippedItems}
                                                    type={getStatType(stat)}
                                                />
                                            </div>
                                        ))}
                                        {treatmentsArray?.length > 0 && (
                                            <div className="flex flex-col gap-1.5">
                                                {treatmentsArray?.map((stat) => (
                                                    <div key={stat} className="flex items-center gap-2">
                                                        <Heart alt="" className="h-5 w-auto text-red-600" />
                                                        <p>{stat}</p>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </p>
                                )}

                            {!isMarketItem && (
                                <p className="text-stroke-sm text-white text-xl">
                                    {displayCurrency()}
                                    {itemCost}
                                </p>
                            )}
                        </div>
                    </div>
                </div>

                <div className="mx-auto my-4 flex h-full gap-8">
                    <div className="flex flex-col">
                        <label
                            className="mb-1 text-center text-gray-500 text-sm uppercase dark:text-gray-400"
                            htmlFor="quantity"
                        >
                            Quantity
                        </label>
                        <label htmlFor="quantity" className="sr-only">
                            Quantity
                        </label>
                        <select
                            id="quantity"
                            name={`quantity${itemToBuy.id}`}
                            value={quantity[`quantity${itemToBuy.id}`] || "1"}
                            className="ml-2 w-24 rounded-md border border-gray-300 text-left font-medium text-gray-700 text-lg shadow-xs focus:border-indigo-500 focus:outline-hidden focus:ring-1 focus:ring-indigo-500 sm:text-sm md:text-base dark:border-gray-500 dark:bg-gray-900 dark:text-white"
                            onChange={handleQuantityChange}
                        >
                            {getQuantityOptions(isMarketItem)}
                        </select>
                    </div>
                    <div className="flex flex-col gap-1">
                        <p className="mb-1 text-center text-gray-500 text-sm uppercase dark:text-gray-400">TOTAL </p>
                        <p className="ml-1 text-3xl text-stroke-s-sm dark:text-gray-100">
                            {isMarketItem ? (
                                <>
                                    {" "}
                                    {displayCurrency()}
                                    {(itemToBuy as AuctionItem)?.buyoutPrice *
                                        Number.parseInt(quantity[`quantity${itemToBuy.id}`] || "1")}
                                </>
                            ) : (
                                <>
                                    {" "}
                                    {displayCurrency()}
                                    {itemCost * Number.parseInt(quantity[`quantity${itemToBuy.id}`] || "1")}
                                </>
                            )}
                        </p>
                    </div>
                </div>

                <button
                    type="button"
                    className="darkBlueButtonBGSVG mx-auto flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 dark:text-slate-200"
                    onClick={handleBuy}
                >
                    Buy
                </button>
            </div>
        </Modal>
    );
}
