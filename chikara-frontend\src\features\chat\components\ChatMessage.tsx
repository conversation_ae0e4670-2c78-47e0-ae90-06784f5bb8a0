import { DisplayAvatar } from "@/components/DisplayAvatar";
import { ChatDropdownMenu } from "@/components/Menu/DropdownMenu";
import { cn } from "@/lib/utils";
import { User } from "@/types/user";
import { UTCDateMini } from "@date-fns/utc";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import RenderChatText from "./RenderChatText";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import type { ChatMessage } from "../types/chat";

interface ChatMessageProps {
    msg: ChatMessage;
    currentUser: User;
    setSelectedReplyMessage: (_message: ChatMessage) => void;
    setFocusChatMsgInput: (_focusChatMsgInput: boolean) => void;
}

export default function ChatMessage({
    msg,
    currentUser,
    setSelectedReplyMessage,
    setFocusChatMsgInput,
}: ChatMessageProps) {
    if (typeof msg.message !== "string") return null;

    const currentUserType = currentUser?.userType;
    const currentUserId = currentUser?.id;

    const renderUserBadge = (userType: string) => {
        if (userType === "admin") {
            return (
                <p className="-translate-x-1/2 absolute bottom-[-12px] left-1/2 rounded-lg bg-red-700 px-1 text-white text-xs md:bottom-[-5px]">
                    Staff
                </p>
            );
        }
        if (userType === "prefect") {
            return (
                <p className="-translate-x-1/2 absolute bottom-[-12px] left-1/2 rounded-lg bg-indigo-700 px-1 text-white text-xs md:bottom-[-5px]">
                    Prefect
                </p>
            );
        }
    };

    const includesMention = () => {
        if (msg.userId === currentUserId) return false;
        const parentMessageUserId = msg?.parentMessage?.userId || null;

        if (parentMessageUserId && parentMessageUserId === currentUserId) return true;
        return false;
    };

    const includesSlashMe = () => {
        if (msg?.message?.startsWith("/me")) return true;
        return false;
    };

    const scrollToMessage = (messageId: number) => {
        const element = document.getElementById(`message-${messageId}`);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    return (
        <div
            id={`message-${msg.id}`}
            className={cn(
                msg?.user?.userType === "admin"
                    ? "chatMessageWrapperAdmin"
                    : "chatMessageWrapper border-2 border-gray-600/25",
                includesMention() ? "chatMessageWrapperMention" : "",
                msg?.parentMessageId ? "max-h-[240px]" : "max-h-[180px]",
                "relative mt-2 min-h-[80px] overflow-hidden rounded-lg bg-[#dde0e6] pt-0.5 pr-2 pb-2 pl-20 shadow-xl dark:bg-[#1F1F2D]"
            )}
        >
            <Link
                className="chatMessageAvatar absolute size-14 rounded-md ring-2 ring-blue-500 2xl:size-16"
                to={"/profile/" + msg.userId}
            >
                <div className="relative">
                    <DisplayAvatar src={msg.user} className="size-14 rounded-md 2xl:size-16" />

                    {renderUserBadge(msg?.user?.userType)}
                    <div className="-left-1 -top-1 absolute flex size-5 rounded-full border-2 border-indigo-900 bg-indigo-600 text-white shadow-sm">
                        <span className="m-auto text-xs">{msg?.user?.level}</span>
                    </div>
                </div>
            </Link>

            <div className="flex flex-row">
                <p
                    className={cn(
                        includesSlashMe() && "hidden",
                        "truncate font-lili text-gray-800 text-sm text-stroke-s-sm md:text-[0.82rem] dark:text-white"
                    )}
                >
                    {msg?.user?.username}
                    {msg?.user?.id === 5 ? (
                        <span className="-py-0.5 ml-1.5 rounded-md border border-black bg-red-500 px-[0.3rem] text-[0.6rem]">
                            AI
                        </span>
                    ) : null}
                </p>
                <small
                    data-tooltip-id="date-tooltip"
                    data-tooltip-content={format(new UTCDateMini(msg.createdAt), "PP, p")}
                    className="mt-0.5 mr-2 ml-auto cursor-pointer text-gray-600 text-xs dark:text-gray-300"
                >
                    {formatTimeToNow(msg.createdAt) ? `${formatTimeToNow(msg.createdAt)} ago` : "-"}
                </small>

                <ChatDropdownMenu
                    userType={currentUserType}
                    msg={msg}
                    setSelectedReplyMessage={setSelectedReplyMessage}
                    setFocusChatMsgInput={setFocusChatMsgInput}
                />
            </div>
            {msg && msg.parentMessageId && msg.parentMessageId > 0 && (
                <div
                    className="mt-0.5 mb-1.5 flex cursor-pointer flex-row truncate rounded-xs border-custom-yellow border-l-4 bg-blue-950 px-1 text-gray-300 text-xs ring-1 ring-gray-600/50"
                    onClick={() => scrollToMessage(msg.parentMessageId as number)}
                >
                    <DisplayAvatar src={msg?.parentMessage?.user} className="mr-2 h-6 w-auto rounded-md p-0.5" />
                    <div className="truncate py-1">
                        {" "}
                        <RenderChatText
                            className="my-auto w-full truncate break-words font-body text-gray-700 text-sm md:text-[0.8rem] md:leading-[1.2rem] dark:text-gray-200"
                            msg={msg?.parentMessage}
                            imgClassName="max-h-5"
                        />
                    </div>
                </div>
            )}
            {msg.hidden ? (
                <p className="my-auto w-[90%] break-words font-body text-gray-500 text-xs dark:text-gray-300">
                    --message removed--
                </p>
            ) : (
                <RenderChatText
                    className="my-auto w-[90%] break-words font-body text-gray-700 text-sm md:text-[0.8rem] md:leading-[1.2rem] dark:text-gray-200"
                    msg={msg}
                />
            )}
        </div>
    );
}
